#!/bin/bash

# UI Testing Script for Email Notification System
# This script helps verify the UI works correctly across different scenarios

echo "🧪 Email Notification UI Testing Script"
echo "========================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

echo ""
echo "📋 Pre-flight Checks"
echo "--------------------"

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
else
    echo "✅ Dependencies are installed"
fi

# Check TypeScript compilation
echo "🔍 Checking TypeScript compilation..."
npm run type-check
if [ $? -eq 0 ]; then
    echo "✅ TypeScript compilation successful"
else
    echo "❌ TypeScript compilation failed"
    exit 1
fi

# Build the project
echo "🏗️  Building project..."
npm run build
if [ $? -eq 0 ]; then
    echo "✅ Build successful"
else
    echo "❌ Build failed"
    exit 1
fi

echo ""
echo "🎯 UI Testing Checklist"
echo "----------------------"
echo ""
echo "Please manually verify the following after starting the dev server:"
echo ""
echo "📱 Mobile Testing (< 640px):"
echo "  [ ] Form elements stack vertically"
echo "  [ ] Input field is full width"
echo "  [ ] Button is full width below input"
echo "  [ ] Text is readable and properly sized"
echo "  [ ] No horizontal scrolling"
echo ""
echo "💻 Desktop Testing (> 640px):"
echo "  [ ] Form elements are horizontal"
echo "  [ ] Input and button are properly sized"
echo "  [ ] Component fits within OutputPanel"
echo "  [ ] Dashed border styling is visible"
echo "  [ ] Text is centered and readable"
echo ""
echo "🔧 Functionality Testing:"
echo "  [ ] Email validation works (try: invalid-email)"
echo "  [ ] Success state displays correctly"
echo "  [ ] Error messages are clear and visible"
echo "  [ ] Rate limiting works (try 4+ quick submissions)"
echo "  [ ] Loading state shows spinner"
echo ""
echo "🌍 Internationalization Testing:"
echo "  [ ] English (/en): All text displays correctly"
echo "  [ ] Chinese (/zh): All text displays correctly"
echo "  [ ] Japanese (/ja): All text displays correctly"
echo ""
echo "🎨 Visual Integration Testing:"
echo "  [ ] Component doesn't break OutputPanel layout"
echo "  [ ] Colors match the existing design system"
echo "  [ ] Spacing and alignment look natural"
echo "  [ ] Success/error states are visually clear"
echo "  [ ] Icons are properly sized and aligned"
echo ""

# Start the development server
echo "🚀 Starting development server..."
echo ""
echo "The server will start at http://localhost:3000"
echo "Navigate to the TTS Workspace section to test the notification component"
echo ""
echo "Test URLs:"
echo "  English: http://localhost:3000/en"
echo "  Chinese: http://localhost:3000/zh"
echo "  Japanese: http://localhost:3000/ja"
echo ""
echo "Press Ctrl+C to stop the server when testing is complete"
echo ""

# Start the dev server
npm run dev
