'use client';

export interface AnalyticsEvent {
  name: string;
  props?: Record<string, unknown>;
}

export class PlausibleAnalytics {
  private readonly domain = 'freeindextts.com';
  private readonly apiEndpoint = 'https://plausible.io/api/event';

  async trackEvent(eventName: string, props?: Record<string, unknown>) {
    if (typeof window === 'undefined') return;

    const event = {
      name: eventName,
      url: window.location.href,
      domain: this.domain,
      props: props || {}
    };

    try {
      await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': navigator.userAgent
        },
        body: JSON.stringify(event)
      });
    } catch (error) {
      console.error('Analytics tracking failed:', error);
    }
  }

  trackPageView(path: string) {
    this.trackEvent('pageview', { path });
  }

  trackTTSGeneration(language: string, textLength: number, hasVoiceFile: boolean, hasEmotion: boolean) {
    this.trackEvent('tts_generation', {
      language,
      textLength,
      hasVoiceFile,
      hasEmotion
    });
  }

  trackFeatureUsage(feature: string) {
    this.trackEvent('feature_usage', { feature });
  }

  trackFileUpload(fileType: 'voice' | 'emotion', fileSize: number) {
    this.trackEvent('file_upload', { fileType, fileSize });
  }

  trackLanguageChange(fromLang: string, toLang: string) {
    this.trackEvent('language_change', { fromLang, toLang });
  }

  trackDemo(demoType: string) {
    this.trackEvent('demo_interaction', { demoType });
  }
}

export function useAnalytics() {
  const analytics = new PlausibleAnalytics();

  return {
    trackEvent: analytics.trackEvent.bind(analytics),
    trackPageView: analytics.trackPageView.bind(analytics),
    trackTTSGeneration: analytics.trackTTSGeneration.bind(analytics),
    trackFeatureUsage: analytics.trackFeatureUsage.bind(analytics),
    trackFileUpload: analytics.trackFileUpload.bind(analytics),
    trackLanguageChange: analytics.trackLanguageChange.bind(analytics),
    trackDemo: analytics.trackDemo.bind(analytics)
  };
}