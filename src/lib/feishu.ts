/**
 * Feishu webhook integration service
 * Handles sending notifications to <PERSON>ishu when users subscribe to email notifications
 */

export interface FeishuMessage {
  msg_type: 'text' | 'rich_text' | 'interactive';
  content: {
    text?: string;
    rich_text?: any;
    card?: any;
  };
}

export interface EmailSubscriptionData {
  email: string;
  timestamp: string;
  ip?: string;
  userAgent?: string;
  source?: string;
}

/**
 * Creates a formatted Feishu message for email subscription
 */
export function createSubscriptionMessage(data: EmailSubscriptionData): FeishuMessage {
  const { email, timestamp, ip, userAgent, source = 'FreeIndexTTS' } = data;
  
  const formattedTime = new Date(timestamp).toLocaleString('en-US', {
    timeZone: 'UTC',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short'
  });

  let messageText = `🔔 New Email Subscription\n\n`;
  messageText += `📧 Email: ${email}\n`;
  messageText += `⏰ Time: ${formattedTime}\n`;
  messageText += `🌐 Source: ${source}\n`;
  
  if (ip && ip !== 'unknown') {
    messageText += `🌍 IP: ${ip}\n`;
  }
  
  if (userAgent) {
    // Truncate user agent if too long
    const truncatedUA = userAgent.length > 100 ? 
      userAgent.substring(0, 100) + '...' : 
      userAgent;
    messageText += `🖥️ User Agent: ${truncatedUA}\n`;
  }

  return {
    msg_type: 'text',
    content: {
      text: messageText
    }
  };
}

/**
 * Creates a rich text Feishu message with better formatting
 */
export function createRichSubscriptionMessage(data: EmailSubscriptionData): FeishuMessage {
  const { email, timestamp, ip, userAgent, source = 'FreeIndexTTS' } = data;
  
  const formattedTime = new Date(timestamp).toLocaleString('en-US', {
    timeZone: 'UTC',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short'
  });

  const richContent = {
    elements: [
      {
        tag: 'div',
        text: {
          tag: 'lark_md',
          content: `**🔔 New Email Subscription**\n\n**📧 Email:** ${email}\n**⏰ Time:** ${formattedTime}\n**🌐 Source:** ${source}`
        }
      }
    ]
  };

  if (ip && ip !== 'unknown') {
    richContent.elements[0].text.content += `\n**🌍 IP:** ${ip}`;
  }

  return {
    msg_type: 'rich_text',
    content: {
      rich_text: richContent
    }
  };
}

/**
 * Sends a message to Feishu webhook
 */
export async function sendFeishuMessage(
  webhookUrl: string, 
  message: FeishuMessage
): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        error: `HTTP ${response.status}: ${errorText}`
      };
    }

    const result = await response.json();
    
    // Feishu returns { "code": 0 } for success
    if (result.code !== 0) {
      return {
        success: false,
        error: `Feishu API error: ${result.msg || 'Unknown error'}`
      };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Sends email subscription notification to Feishu
 */
export async function notifyEmailSubscription(
  webhookUrl: string,
  subscriptionData: EmailSubscriptionData,
  useRichText: boolean = false
): Promise<{ success: boolean; error?: string }> {
  const message = useRichText 
    ? createRichSubscriptionMessage(subscriptionData)
    : createSubscriptionMessage(subscriptionData);

  return sendFeishuMessage(webhookUrl, message);
}

/**
 * Validates Feishu webhook URL format
 */
export function validateFeishuWebhookUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.hostname === 'open.feishu.cn' && 
           parsedUrl.pathname.startsWith('/open-apis/bot/v2/hook/');
  } catch {
    return false;
  }
}

/**
 * Default Feishu webhook URL (can be overridden via environment)
 */
export const DEFAULT_FEISHU_WEBHOOK_URL = 'https://open.feishu.cn/open-apis/bot/v2/hook/8b3a45cd-2d0e-4269-8edf-9fb1009ce659';
