'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Clock, Volume2, Download, Zap } from 'lucide-react';
import { EmailNotification } from '../ui/EmailNotification';

export function OutputPanel() {
  const { t } = useTranslation();

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 h-full flex flex-col">
      <div className="flex items-center space-x-2 mb-6">
        <Volume2 className="w-5 h-5 text-primary-600" />
        <h3 className="text-xl font-semibold text-gray-900">
          {t('workspace.output.title', 'Output')}
        </h3>
      </div>

      {/* Coming Soon Content */}
      <div className="flex flex-col items-center justify-center flex-1 text-center py-8 space-y-8">
        {/* <div className="w-24 h-24 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
          <Clock className="w-12 h-12 text-green-600" />
        </div>
        <h4 className="text-2xl font-bold text-gray-900 mb-3">
          {t('workspace.output.comingSoon', 'Coming Soon')}
        </h4>
        <p className="text-gray-600 max-w-md mb-6">
          {t('workspace.output.comingSoonDesc', 'High-quality AI-generated speech output will be available here. Experience natural-sounding voices with full control.')}
        </p> */}

        {/* Feature Preview */}
        <div className="grid grid-cols-2 gap-4 w-full max-w-md mb-8">
          <div className="bg-gray-50 rounded-lg p-4">
            <Volume2 className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">{t('workspace.output.features.highQuality', 'High Quality Audio')}</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <Download className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">{t('workspace.output.features.instantDownload', 'Instant Download')}</p>
          </div>
        </div>

        {/* Waveform Preview */}
        <div className="w-full max-w-md mb-8">
          {/* <div className="bg-gray-100 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-center space-x-1">
              {Array.from({ length: 20 }).map((_, i) => (
                <div
                  key={i}
                  className="w-1 bg-gradient-to-t from-blue-400 to-purple-400 rounded-full opacity-50"
                  style={{
                    height: `${Math.random() * 32 + 8}px`,
                    animationDelay: `${i * 0.1}s`
                  }}
                />
              ))}
            </div>
          </div> */}

          {/* Progress Indicator */}
          {/* <div className="flex justify-between text-sm text-gray-500 mb-2">
            <span>{t('workspace.output.progress', 'Audio Engine Progress')}</span>
            <span>85%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full w-4/5 transition-all duration-300"></div>
          </div> */}
        </div>

        {/* Email Notification */}
        <div className="w-full max-w-md px-4">
          <EmailNotification
            variant="compact"
            onSuccess={(email) => {
              console.log('TTS Workspace notification subscription:', email);
            }}
            onError={(error) => {
              console.error('TTS Workspace notification error:', error);
            }}
          />
        </div>
      </div>
    </div>
  );
}