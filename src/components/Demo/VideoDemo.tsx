import React from 'react';
import { useTranslation } from 'react-i18next';
import { Play, Volume2, Mic, Settings } from 'lucide-react';

export function VideoDemo() {
  const { t } = useTranslation();

  const demos = [
    {
      id: 'emotion-dubbing',
      title: t('demo.samples.emotionDubbing.title', 'Emotion Dubbing'),
      description: t('demo.samples.emotionDubbing.description', 'Experience emotional voice dubbing with AI-powered IndexTTS2 technology'),
      thumbnail: '/images/demo-emotion-dubbing.jpg',
      videoUrl: 'https://video.twimg.com/amplify_video/1944518738717401088/vid/avc1/1280x720/6XA9YS-6p588LKsN.mp4?tag=14',
      duration: '0:40'
    },
    {
      id: 'voice-cloning',
      title: t('demo.samples.voiceCloning.title', 'Voice Cloning'),
      description: t('demo.samples.voiceCloning.description', 'Zero-shot voice cloning demonstration'),
      thumbnail: '/images/demo-voice-cloning.jpg',
      videoUrl: 'https://index-tts.github.io/index-tts2.github.io/ex6/Empresses_in_the_Palace_1.mp4',
      duration: '2:57'
    },
    {
      id: 'emotion-control',
      title: t('demo.samples.emotionControl.title', 'Emotion Control'),
      description: t('demo.samples.emotionControl.description', 'Control emotional expressions in generated speech'),
      thumbnail: '/images/demo-emotion-control.jpg',
      videoUrl: 'https://index-tts.github.io/index-tts2.github.io/ex6/Empresses_in_the_Palace_2.mp4',
      duration: '2:47'
    }
  ];

  const handlePlayDemo = (demoId: string, videoElement: HTMLVideoElement) => {
    console.log(`Playing demo: ${demoId}`);
    if (videoElement.paused) {
      videoElement.play();
    } else {
      videoElement.pause();
    }
  };

  return (
    <section id="demo" className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            {t('demo.title', 'See It in Action')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('demo.description', 'Watch how FreeIndexTTS transforms text into natural-sounding speech with advanced AI technology.')}
          </p>
        </div>

        {/* Demo Videos Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {demos.map((demo) => (
            <div
              key={demo.id}
              className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
            >
              {/* Video Player */}
              <div className="relative aspect-video bg-gray-100 overflow-hidden">
                <video
                  src={demo.videoUrl}
                  className="w-full h-full object-cover cursor-pointer"
                  preload="metadata"
                  muted
                  playsInline
                  controls
                  onLoadedMetadata={(e) => {
                    e.currentTarget.currentTime = 0.1;
                  }}
                  onClick={(e) => handlePlayDemo(demo.id, e.currentTarget)}
                />

                {/* Duration Badge */}
                <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-sm px-2 py-1 rounded pointer-events-none">
                  {demo.duration}
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {demo.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {demo.description}
                </p>
                
                <button
                  onClick={(e) => {
                    const videoElement = e.currentTarget.closest('.bg-white')?.querySelector('video') as HTMLVideoElement;
                    if (videoElement) {
                      handlePlayDemo(demo.id, videoElement);
                      videoElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                  }}
                  className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 font-medium text-sm transition-colors"
                >
                  <Play size={16} />
                  <span>{t('demo.watchDemo', 'Watch Demo')}</span>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Features List */}
        <div className="grid lg:grid-cols-3 gap-8 items-start">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <Volume2 className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">
                {t('demo.features.naturalVoices', 'Natural Voices')}
              </h3>
              <p className="text-gray-600">
                {t('demo.features.naturalVoicesDesc', 'Generate speech that sounds completely human with proper intonation and emotion.')}
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <Mic className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">
                {t('demo.features.voiceCloning', 'Voice Cloning')}
              </h3>
              <p className="text-gray-600">
                {t('demo.features.voiceCloningDesc', 'Clone any voice from a short audio sample with zero-shot learning.')}
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <Settings className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">
                {t('demo.features.fullControl', 'Full Control')}
              </h3>
              <p className="text-gray-600">
                {t('demo.features.fullControlDesc', 'Adjust speed, pitch, emotion, and duration to perfect your audio output.')}
              </p>
            </div>
          </div>
        </div>

        {/* IndexTTS2 Notice */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
            {t('demo.indexTTSNotice', 'Official IndexTTS2 demo videos showcasing breakthrough AI voice synthesis')}
          </div>
        </div>
      </div>
    </section>
  );
}