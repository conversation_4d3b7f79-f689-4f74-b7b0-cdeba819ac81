# FreeIndexTTS - Advanced AI Text-to-Speech

[![Deploy to Cloudflare Pages](https://img.shields.io/badge/Deploy%20to-Cloudflare%20Pages-orange)](https://pages.cloudflare.com/)
[![Vite](https://img.shields.io/badge/Built%20with-Vite-646CFF)](https://vitejs.dev/)
[![React](https://img.shields.io/badge/React-18.2.0-blue)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.2.2-blue)](https://www.typescriptlang.org/)

Advanced AI text-to-speech with IndexTTS2 - emotion control and voice cloning capabilities.

## 🚀 Recent Updates

### Project Migration: Next.js → Vite
The project has been successfully migrated from Next.js to Vite for better performance and simpler deployment on Cloudflare Pages.

#### Key Changes:
- **Framework**: Migrated from Next.js to Vite + React
- **Internationalization**: Switched from `next-intl` to `react-i18next`
- **Routing**: Implemented React Router for client-side navigation
- **Build System**: Replaced Next.js build with Vite's faster build system
- **Deployment**: Optimized for Cloudflare Pages deployment
- **Bundle Size**: Reduced bundle size with better code splitting

## 🌟 Features

- **Zero-shot Voice Cloning**: Clone any voice from a short audio sample
- **Emotion Control**: Adjust emotional tone in generated speech
- **Duration Control**: Control speech speed and rhythm
- **Multi-language Support**: English, Chinese, Japanese, and more
- **Real-time Processing**: Fast generation for immediate results
- **Privacy First**: Secure processing with user privacy protection

## 🎬 Demo Videos

The application includes official IndexTTS2 demo videos showcasing:
- Emotion dubbing capabilities
- Voice cloning demonstrations
- Emotion control features

## 🛠️ Development

### Prerequisites
- Node.js 18.0.0 or higher
- npm 8.0.0 or higher

### Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking
- `npm run deploy` - Deploy to Cloudflare Pages

## 📦 Deployment

The application is optimized for Cloudflare Pages deployment:

```bash
# Deploy to Cloudflare Pages
npm run deploy
```

### Deployment Configuration
- **Build Command**: `npm run build`
- **Build Output Directory**: `dist`
- **Node.js Version**: 18.x

## 🎨 UI/UX Status

### Current State
- **TTS Workspace**: Currently displays "Coming Soon" with progress indicators
- **Demo Section**: Fully functional with official IndexTTS2 demo videos
- **Feature Showcase**: Complete feature overview and descriptions

### Planned Features
- Full TTS input interface (75% complete)
- Audio output player (85% complete)
- Voice cloning integration
- Emotion control settings

## 🌐 Multi-language Support

- **English** (Default)
- **中文** (Chinese)
- **日本語** (Japanese)

Language routing is handled client-side with automatic detection and fallback.

## 🔧 Technical Stack

- **Frontend**: React 18.2.0, TypeScript 5.2.2
- **Build Tool**: Vite 5.0.0
- **Styling**: Tailwind CSS 3.3.5
- **Internationalization**: react-i18next 13.5.0
- **Routing**: React Router 6.20.0
- **Icons**: Lucide React
- **Deployment**: Cloudflare Pages

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── Demo/           # Demo video components
│   ├── FAQ/            # FAQ section
│   ├── Features/       # Feature showcase
│   ├── Footer/         # Footer component
│   ├── Header/         # Header with navigation
│   ├── Hero/           # Hero section
│   ├── TTS/            # TTS workspace (Coming Soon)
│   └── ui/             # UI components
├── lib/                # Utility functions
├── messages/           # Internationalization files
├── pages/              # Page components
├── styles/             # Global styles
└── main.tsx           # Application entry point
```

## 🚀 Performance Optimizations

- **Code Splitting**: Separate chunks for vendor, router, and i18n
- **Lazy Loading**: Components loaded on demand
- **Asset Optimization**: Optimized images and fonts
- **Bundle Analysis**: Tree-shaking and dead code elimination

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

This project is currently in development. Contributions will be welcome once the initial version is complete.

## 🔗 Links

- **Demo**: [https://freeindextts.pages.dev](https://freeindextts.pages.dev)
- **IndexTTS2 Research**: [Official Paper](https://arxiv.org/abs/2506.21619)
- **Official Demos**: [IndexTTS2 Demo Site](https://index-tts.github.io/index-tts2.github.io/)

---

*Built with ❤️ using modern web technologies*