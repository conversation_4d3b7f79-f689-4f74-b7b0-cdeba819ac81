interface Env {
  NOTIFICATIONS_KV: KVNamespace;
  FEISHU_WEBHOOK_URL: string;
}

interface PagesContext {
  request: Request;
  env: Env;
  params: Record<string, string>;
  data: Record<string, any>;
  next: () => Promise<Response>;
  waitUntil: (promise: Promise<any>) => void;
}

type PagesFunction<T = unknown> = (context: PagesContext & { env: T }) => Promise<Response> | Response;

interface EmailSubscription {
  email: string;
  timestamp: string;
  ip?: string;
  userAgent?: string;
}

interface FeishuMessage {
  msg_type: 'text' | 'rich_text';
  content: {
    text?: string;
    rich_text?: any;
  };
}

// Rate limiting: Store IP addresses with timestamps
const MAX_REQUESTS_PER_WINDOW = 3;

async function checkRateLimit(env: Env, ip: string): Promise<boolean> {
  const key = `rate_limit:${ip}`;
  const existing = await env.NOTIFICATIONS_KV.get(key);
  
  if (!existing) {
    await env.NOTIFICATIONS_KV.put(key, '1', { expirationTtl: 60 });
    return true;
  }
  
  const count = parseInt(existing);
  if (count >= MAX_REQUESTS_PER_WINDOW) {
    return false;
  }
  
  await env.NOTIFICATIONS_KV.put(key, (count + 1).toString(), { expirationTtl: 60 });
  return true;
}

async function validateEmail(email: string): Promise<boolean> {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

async function checkDuplicateEmail(env: Env, email: string): Promise<boolean> {
  const existing = await env.NOTIFICATIONS_KV.get(`email:${email.toLowerCase()}`);
  return existing !== null;
}

async function storeEmail(env: Env, subscription: EmailSubscription): Promise<void> {
  try {
    console.log('Storing email subscription:', subscription.email);

    // Check if KV is available
    if (!env.NOTIFICATIONS_KV) {
      throw new Error('NOTIFICATIONS_KV binding is not available');
    }

    const key = `email:${subscription.email.toLowerCase()}`;
    await env.NOTIFICATIONS_KV.put(key, JSON.stringify(subscription));
    console.log('Successfully stored individual email:', key);

    // Also store in a list for easy retrieval
    const listKey = 'email_list';
    const existingList = await env.NOTIFICATIONS_KV.get(listKey);
    const emails = existingList ? JSON.parse(existingList) : [];
    emails.push(subscription);
    await env.NOTIFICATIONS_KV.put(listKey, JSON.stringify(emails));
    console.log('Successfully updated email list, total emails:', emails.length);
  } catch (error) {
    console.error('Error storing email:', error);
    throw error;
  }
}

async function sendFeishuNotification(env: Env, subscription: EmailSubscription): Promise<void> {
  const webhookUrl = env.FEISHU_WEBHOOK_URL || 'https://open.feishu.cn/open-apis/bot/v2/hook/8b3a45cd-2d0e-4269-8edf-9fb1009ce659';

  try {
    // 获取当前总订阅人数
    const totalSubscribers = await getTotalSubscriberCount(env);

    const formattedTime = new Date(subscription.timestamp).toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    // 构建markdown格式的消息内容
    let messageContent = `**🎉 新用户订阅通知**\n\n`;
    messageContent += `📧 **邮箱地址**: ${subscription.email}\n`;
    messageContent += `📊 **总订阅人数**: ${totalSubscribers}人\n`;
    messageContent += `⏰ **订阅时间**: ${formattedTime}\n`;
    messageContent += `🌐 **来源**: FreeIndexTTS 通知系统\n`;

    if (subscription.ip && subscription.ip !== 'unknown') {
      messageContent += `🌍 **IP地址**: ${subscription.ip}\n`;
    }

    if (subscription.userAgent) {
      const truncatedUA = subscription.userAgent.length > 80 ?
        subscription.userAgent.substring(0, 80) + '...' :
        subscription.userAgent;
      messageContent += `🖥️ **用户代理**: ${truncatedUA}\n`;
    }

    // 添加里程碑提醒
    const milestoneMessage = getMilestoneMessage(totalSubscribers);
    if (milestoneMessage) {
      messageContent += `\n${milestoneMessage}`;
    }

    // 构建飞书卡片消息
    const payload = {
      msg_type: "interactive",
      card: {
        elements: [
          {
            tag: "div",
            text: {
              content: messageContent,
              tag: "lark_md"
            }
          },
          { tag: "hr" },
          {
            tag: "note",
            elements: [
              {
                tag: "plain_text",
                content: `通知时间: ${formattedTime} | FreeIndexTTS 邮件订阅系统`
              }
            ]
          }
        ],
        header: {
          template: "green",
          title: {
            content: "📬 新邮件订阅通知",
            tag: "plain_text"
          }
        }
      }
    };

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to send Feishu notification:', response.status, response.statusText, errorText);
    } else {
      const result = await response.json();
      if (result.code !== 0) {
        console.error('Feishu API error:', result.msg || 'Unknown error');
      } else {
        console.log('Feishu notification sent successfully');
      }
    }
  } catch (error) {
    console.error('Error sending Feishu notification:', error);
  }
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
  const { request, env } = context;

  console.log('Notification API called');
  console.log('Environment bindings available:', Object.keys(env));
  console.log('NOTIFICATIONS_KV available:', !!env.NOTIFICATIONS_KV);

  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  };

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('CF-Connecting-IP') || 
                    request.headers.get('X-Forwarded-For') || 
                    'unknown';

    // Check rate limit
    const rateLimitOk = await checkRateLimit(env, clientIP);
    if (!rateLimitOk) {
      return new Response(
        JSON.stringify({ error: 'Too many requests. Please try again later.' }),
        { 
          status: 429, 
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        }
      );
    }

    // Parse request body
    const body = await request.json() as { email: string };
    const email = body.email?.trim();

    if (!email) {
      return new Response(
        JSON.stringify({ error: 'Email address is required' }),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        }
      );
    }

    // Validate email format
    const isValidEmail = await validateEmail(email);
    if (!isValidEmail) {
      return new Response(
        JSON.stringify({ error: 'Invalid email address format' }),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        }
      );
    }

    // Check for duplicate email
    const isDuplicate = await checkDuplicateEmail(env, email);
    if (isDuplicate) {
      return new Response(
        JSON.stringify({ error: 'This email is already subscribed' }),
        { 
          status: 409, 
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        }
      );
    }

    // Create subscription object
    const subscription: EmailSubscription = {
      email: email.toLowerCase(),
      timestamp: new Date().toISOString(),
      ip: clientIP,
      userAgent: request.headers.get('User-Agent') || undefined,
    };

    // Store email in KV
    await storeEmail(env, subscription);

    // Send Feishu notification (don't await to avoid blocking response)
    sendFeishuNotification(env, subscription).catch(console.error);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Successfully subscribed to notifications' 
      }),
      { 
        status: 200, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );

  } catch (error) {
    console.error('Error processing subscription:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );
  }
};
