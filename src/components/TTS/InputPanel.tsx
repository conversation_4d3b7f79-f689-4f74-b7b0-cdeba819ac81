'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Clock, Mic, Type, Settings } from 'lucide-react';

export function InputPanel() {
  const { t } = useTranslation();

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center space-x-2 mb-6">
        <Type className="w-5 h-5 text-primary-600" />
        <h3 className="text-xl font-semibold text-gray-900">
          {t('workspace.input.title', 'Input')}
        </h3>
      </div>

      {/* Coming Soon Content */}
      <div className="flex flex-col items-center justify-center h-96 text-center">
        <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
          <Clock className="w-12 h-12 text-blue-600" />
        </div>
        <h4 className="text-2xl font-bold text-gray-900 mb-3">
          {t('workspace.input.comingSoon', 'Coming Soon')}
        </h4>
        <p className="text-gray-600 max-w-md mb-6">
          {t('workspace.input.comingSoonDesc', 'The AI-powered text-to-speech interface will be available soon. Stay tuned for advanced voice synthesis capabilities.')}
        </p>
        
        {/* Feature Preview */}
        <div className="grid grid-cols-2 gap-4 w-full max-w-md">
          <div className="bg-gray-50 rounded-lg p-4">
            <Mic className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">{t('workspace.input.features.voiceCloning', 'Voice Cloning')}</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <Settings className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">{t('workspace.input.features.emotionControl', 'Emotion Control')}</p>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="mt-8 w-full max-w-sm">
          <div className="flex justify-between text-sm text-gray-500 mb-2">
            <span>{t('workspace.input.progress', 'Development Progress')}</span>
            <span>75%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full w-3/4 transition-all duration-300"></div>
          </div>
        </div>
      </div>
    </div>
  );
}