/**
 * Email validation utilities for the notification system
 */

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validates email format using RFC 5322 compliant regex
 */
export function validateEmailFormat(email: string): ValidationResult {
  if (!email || typeof email !== 'string') {
    return {
      isValid: false,
      error: 'Email address is required'
    };
  }

  const trimmedEmail = email.trim();
  
  if (trimmedEmail.length === 0) {
    return {
      isValid: false,
      error: 'Email address is required'
    };
  }

  if (trimmedEmail.length > 254) {
    return {
      isValid: false,
      error: 'Email address is too long (maximum 254 characters)'
    };
  }

  // RFC 5322 compliant email regex (simplified but robust)
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  
  if (!emailRegex.test(trimmedEmail)) {
    return {
      isValid: false,
      error: 'Please enter a valid email address'
    };
  }

  // Additional checks for common issues
  if (trimmedEmail.includes('..')) {
    return {
      isValid: false,
      error: 'Email address cannot contain consecutive dots'
    };
  }

  if (trimmedEmail.startsWith('.') || trimmedEmail.endsWith('.')) {
    return {
      isValid: false,
      error: 'Email address cannot start or end with a dot'
    };
  }

  const [localPart, domain] = trimmedEmail.split('@');
  
  if (localPart.length > 64) {
    return {
      isValid: false,
      error: 'Email local part is too long (maximum 64 characters)'
    };
  }

  if (domain.length > 253) {
    return {
      isValid: false,
      error: 'Email domain is too long (maximum 253 characters)'
    };
  }

  return {
    isValid: true
  };
}

/**
 * Validates email against common disposable email providers
 */
export function validateEmailProvider(email: string): ValidationResult {
  const disposableProviders = [
    '10minutemail.com',
    'guerrillamail.com',
    'mailinator.com',
    'tempmail.org',
    'throwaway.email',
    'temp-mail.org',
    'yopmail.com',
    'maildrop.cc',
    'sharklasers.com',
    'guerrillamailblock.com'
  ];

  const domain = email.split('@')[1]?.toLowerCase();
  
  if (disposableProviders.includes(domain)) {
    return {
      isValid: false,
      error: 'Disposable email addresses are not allowed'
    };
  }

  return {
    isValid: true
  };
}

/**
 * Comprehensive email validation
 */
export function validateEmail(email: string, options: {
  checkDisposable?: boolean;
} = {}): ValidationResult {
  // First check format
  const formatResult = validateEmailFormat(email);
  if (!formatResult.isValid) {
    return formatResult;
  }

  // Check disposable providers if requested
  if (options.checkDisposable) {
    const providerResult = validateEmailProvider(email);
    if (!providerResult.isValid) {
      return providerResult;
    }
  }

  return {
    isValid: true
  };
}

/**
 * Client-side rate limiting using localStorage
 */
export class ClientRateLimit {
  private key: string;
  private maxRequests: number;
  private windowMs: number;

  constructor(key: string = 'email_subscription_rate_limit', maxRequests: number = 3, windowMs: number = 60000) {
    this.key = key;
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  /**
   * Check if the current request is within rate limits
   */
  checkLimit(): { allowed: boolean; resetTime?: number } {
    try {
      const stored = localStorage.getItem(this.key);
      const now = Date.now();
      
      if (!stored) {
        this.recordRequest(now);
        return { allowed: true };
      }

      const data = JSON.parse(stored);
      const { requests, windowStart } = data;

      // Check if we're in a new window
      if (now - windowStart > this.windowMs) {
        this.recordRequest(now);
        return { allowed: true };
      }

      // Check if we've exceeded the limit
      if (requests >= this.maxRequests) {
        const resetTime = windowStart + this.windowMs;
        return { 
          allowed: false, 
          resetTime 
        };
      }

      // Increment the request count
      this.recordRequest(now, requests + 1, windowStart);
      return { allowed: true };
    } catch (error) {
      // If localStorage is not available or corrupted, allow the request
      console.warn('Rate limiting error:', error);
      return { allowed: true };
    }
  }

  /**
   * Record a request in localStorage
   */
  private recordRequest(now: number, requests: number = 1, windowStart?: number): void {
    try {
      const data = {
        requests,
        windowStart: windowStart || now
      };
      localStorage.setItem(this.key, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to record rate limit:', error);
    }
  }

  /**
   * Reset the rate limit (useful for testing)
   */
  reset(): void {
    try {
      localStorage.removeItem(this.key);
    } catch (error) {
      console.warn('Failed to reset rate limit:', error);
    }
  }

  /**
   * Get time until reset
   */
  getTimeUntilReset(): number {
    try {
      const stored = localStorage.getItem(this.key);
      if (!stored) return 0;

      const data = JSON.parse(stored);
      const { windowStart } = data;
      const resetTime = windowStart + this.windowMs;
      const now = Date.now();

      return Math.max(0, resetTime - now);
    } catch (error) {
      return 0;
    }
  }
}

/**
 * Format time remaining for user display
 */
export function formatTimeRemaining(ms: number): string {
  const seconds = Math.ceil(ms / 1000);
  
  if (seconds < 60) {
    return `${seconds} second${seconds !== 1 ? 's' : ''}`;
  }
  
  const minutes = Math.ceil(seconds / 60);
  return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
}
