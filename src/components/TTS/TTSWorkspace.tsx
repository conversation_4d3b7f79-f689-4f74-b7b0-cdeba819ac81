import React from 'react';
import { useTranslation } from 'react-i18next';
import { InputPanel } from './InputPanel';
import { OutputPanel } from './OutputPanel';

export function TTSWorkspace() {
  const { t } = useTranslation();

  return (
    <section id="workspace" className="py-16 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            {t('workspace.title', 'TTS Workspace')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('workspace.description', 'Transform your text into lifelike speech with advanced AI technology')}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
          <InputPanel />
          <OutputPanel />
        </div>
      </div>
    </section>
  );
}