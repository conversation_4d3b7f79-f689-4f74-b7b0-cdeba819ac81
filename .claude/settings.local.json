{"permissions": {"allow": ["WebFetch(domain:opennext.js.org)", "WebFetch(domain:arxiv.org)", "Bash(npm install)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "Bash(npm run deploy:build:*)", "Bash(ls:*)", "Bash(npx opennextjs-cloudflare upload:*)", "Bash(npm run deploy:upload:*)", "Bash(git add:*)", "Bash(npm run deploy:*)", "WebFetch(domain:freeindextts.com)", "WebFetch(domain:freeindextts.macros-hekk.workers.dev)", "Bash(wrangler routes:*)", "Bash(wrangler deployments:*)", "<PERSON><PERSON>(wrangler:*)", "Bash(npm run clean:*)", "<PERSON><PERSON>(chmod:*)", "Bash(claude-code settings)", "Bash(find:*)"], "deny": []}}