import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { defaultLocale } from '../lib/i18n';

export function NotFoundPage() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-9xl font-bold text-primary-600 mb-4">404</h1>
        <h2 className="text-3xl font-semibold text-gray-900 mb-4">
          {t('notFound.title', 'Page Not Found')}
        </h2>
        <p className="text-lg text-gray-600 mb-8">
          {t('notFound.description', 'The page you are looking for does not exist.')}
        </p>
        <Link
          to={`/${defaultLocale}`}
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors"
        >
          {t('notFound.backHome', 'Back to Home')}
        </Link>
      </div>
    </div>
  );
} 