{"meta": {"title": "FreeIndexTTS - IndexTTS2 AI Text-to-Speech with Voice Cloning & Emotion Control", "description": "Experience IndexTTS2: Revolutionary AI text-to-speech with voice cloning, emotion control and duration precision. Free IndexTTS online TTS tool with zero-shot voice cloning and 配音生成 dubbing generation.", "keywords": "IndexTTS2, indextts, free indextts, text to speech, 文本转语音, TTS, voice cloning, AI voice, emotion control, 配音生成, voice synthesis, dubbing, speech generation, free TTS, voice dubbing"}, "header": {"title": "FreeIndexTTS", "nav": {"home": "Home", "demo": "Demo", "features": "Features", "faq": "FAQ"}}, "hero": {"title": "Revolutionary IndexTTS2 AI Voice Synthesis", "subtitle": "IndexTTS2-powered text-to-speech with emotion control and zero-shot voice cloning", "description": "Experience the most realistic and expressive IndexTTS2 text-to-speech model. Clone any voice with free IndexTTS, control emotions, and generate perfect dubbing with duration precision.", "cta": "Try Now", "features": {"voiceCloning": "Zero-Shot Voice Cloning", "emotionControl": "Emotion Control", "durationControl": "Duration Control", "multilingual": "Multilingual Support"}}, "workspace": {"title": "IndexTTS2 Text-to-Speech Workspace", "input": {"title": "Input - Coming Soon", "textPlaceholder": "Enter your text for IndexTTS2 voice synthesis...", "voiceUpload": "Upload Voice Sample", "voiceUploadDesc": "Upload an audio file to clone the voice", "emotionControl": "Emotion Control", "emotionUpload": "Upload Emotion Sample", "emotionText": "Or describe emotions in text", "emotionPlaceholder": "e.g., excited, calm, whispering, dramatic", "duration": "Duration Control", "durationAuto": "Auto", "durationCustom": "Custom (seconds)", "language": "Language", "generate": "Generate Audio"}, "output": {"title": "Output", "comingSoon": "Coming Soon", "comingSoonDesc": "IndexTTS2 integration is in development. Stay tuned for the most advanced AI text-to-speech and 配音生成 dubbing generation technology!", "processing": "Processing...", "completed": "Audio Generated", "download": "Download Audio", "play": "Play"}}, "demo": {"title": "Experience IndexTTS2 in Action", "subtitle": "See the revolutionary capabilities of emotion-controlled voice synthesis", "samples": {"emotionDubbing": {"title": "Emotional Dubbing Demo", "description": "Chinese to English dubbing with emotion preservation - watch how IndexTTS2 maintains the original emotional performance"}, "voiceCloning": {"title": "Zero-Shot Voice Cloning", "description": "Clone any voice with just a single audio sample - no training required"}, "emotionControl": {"title": "Text-Based Emotion Control", "description": "Control emotions through text descriptions - whisper, shout, express joy or sadness"}}}, "features": {"title": "Breakthrough Features", "subtitle": "IndexTTS2 introduces world-first capabilities in AI voice synthesis", "list": {"zeroShot": {"title": "Zero-Shot Voice Cloning", "description": "Clone any voice with just one audio sample. More accurate than MaskGCT and F5-TTS."}, "emotionCloning": {"title": "Emotion Cloning", "description": "World-first emotion cloning from audio samples or text descriptions."}, "durationControl": {"title": "Perfect Duration Control", "description": "Control exact output length - perfect for dubbing movies and videos."}, "multilingual": {"title": "Multilingual Support", "description": "Supports English and Chinese with plans for more languages."}, "localProcessing": {"title": "Fully Local", "description": "Open weights model that runs completely local - your data stays private."}, "realtimeGeneration": {"title": "High Quality Output", "description": "Professional-grade audio quality suitable for production use."}}}, "faq": {"title": "Frequently Asked Questions", "items": {"whatIs": {"question": "What is IndexTTS2?", "answer": "IndexTTS2 is a breakthrough text-to-speech model that offers zero-shot voice cloning, emotion control, and duration precision. It's the most realistic and expressive TTS model available."}, "howWorks": {"question": "How does voice cloning work?", "answer": "Simply upload one audio sample of the target voice. IndexTTS2 will analyze the voice characteristics and generate speech that sounds identical to the original speaker."}, "emotionControl": {"question": "How can I control emotions?", "answer": "You can either upload an audio sample with the desired emotion or describe the emotion in text (e.g., 'excited', 'whispering', 'dramatic')."}, "languages": {"question": "What languages are supported?", "answer": "Currently, IndexTTS2 supports English and Chinese. More languages will be added in future updates."}, "privacy": {"question": "Is my data safe?", "answer": "Yes! IndexTTS2 is designed to run locally with open weights. Your audio files and text never leave your device."}, "commercial": {"question": "Can I use this commercially?", "answer": "Yes, IndexTTS2 is released under Apache 2 license for both source code and weights, allowing commercial use."}}}, "footer": {"description": "Advanced AI text-to-speech powered by IndexTTS2", "links": {"about": "About", "privacy": "Privacy", "terms": "Terms", "contact": "Contact"}, "copyright": "© 2025 FreeIndexTTS. All rights reserved."}, "notification": {"title": "Get notified when we launch", "description": "Be the first to know when new features are available", "sectionTitle": "Stay Updated", "sectionDescription": "IndexTTS2 is coming soon with revolutionary voice cloning and emotion control. Be among the first to experience the future of AI text-to-speech.", "emailPlaceholder": "Enter your email address", "subscribe": "Notify Me", "success": "Thank you! We'll notify you when features go live.", "successTitle": "You're all set!", "subscribeAnother": "Subscribe another email", "privacy": "We respect your privacy. Unsubscribe at any time.", "additionalInfo": "Join thousands of developers and content creators waiting for IndexTTS2", "features": {"instant": "Instant notifications", "exclusive": "Exclusive early access", "updates": "Feature updates"}, "errors": {"emailRequired": "Email address is required", "emailInvalid": "Please enter a valid email address", "submitFailed": "Failed to subscribe. Please try again.", "rateLimited": "Too many requests. Please try again later.", "disposableEmail": "Disposable email addresses are not allowed", "emailTooLong": "Email address is too long", "alreadySubscribed": "This email is already subscribed"}}}