<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>FreeIndexTTS - Advanced AI Text-to-Speech</title>
  <meta name="description" content="Advanced AI text-to-speech with IndexTTS2 - emotion control and voice cloning" />
  <meta name="keywords" content="text-to-speech, tts, voice-cloning, ai, indextts2, emotion-control, multilingual" />
  <meta name="robots" content="index, follow" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://freeindextts.com/" />
  <meta property="og:title" content="FreeIndexTTS - Advanced AI Text-to-Speech" />
  <meta property="og:description" content="Advanced AI text-to-speech with IndexTTS2 - emotion control and voice cloning" />
  <meta property="og:image" content="https://freeindextts.com/og-image.jpg" />
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://freeindextts.com/" />
  <meta property="twitter:title" content="FreeIndexTTS - Advanced AI Text-to-Speech" />
  <meta property="twitter:description" content="Advanced AI text-to-speech with IndexTTS2 - emotion control and voice cloning" />
  <meta property="twitter:image" content="https://freeindextts.com/og-image.jpg" />
  
  <!-- Font preloads -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
  
  <!-- Structured data -->
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "FreeIndexTTS",
      "description": "Advanced AI text-to-speech with emotion control and voice cloning",
      "url": "https://freeindextts.com",
      "applicationCategory": "MultimediaApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        "Zero-shot voice cloning",
        "Emotion control",
        "Duration control",
        "Multi-language support"
      ],
      "inLanguage": ["en", "zh", "ja"]
    }
  </script>
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  
  <style>
    /* Critical CSS to prevent FOUC */
    body {
      font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background-color: #ffffff;
      color: #1f2937;
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    * {
      box-sizing: border-box;
    }
    #root {
      min-height: 100vh;
    }
  </style>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>
</html> 