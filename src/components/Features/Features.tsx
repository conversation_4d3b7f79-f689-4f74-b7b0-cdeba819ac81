'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Mic, Wand2, Clock, Globe, Zap, Shield } from 'lucide-react';

export function Features() {
  const { t } = useTranslation();

  const features = [
    {
      icon: Mic,
      title: t('features.voiceCloning.title', 'Zero-shot Voice Cloning'),
      description: t('features.voiceCloning.description', 'Clone any voice from a short audio sample without training.'),
      color: 'bg-blue-500'
    },
    {
      icon: Wand2,
      title: t('features.emotionControl.title', 'Emotion Control'),
      description: t('features.emotionControl.description', 'Adjust the emotional tone of the generated speech.'),
      color: 'bg-purple-500'
    },
    {
      icon: Clock,
      title: t('features.durationControl.title', 'Duration Control'),
      description: t('features.durationControl.description', 'Control the speed and rhythm of speech generation.'),
      color: 'bg-green-500'
    },
    {
      icon: Globe,
      title: t('features.multilingual.title', 'Multi-language Support'),
      description: t('features.multilingual.description', 'Generate speech in multiple languages with native pronunciation.'),
      color: 'bg-orange-500'
    },
    {
      icon: Zap,
      title: t('features.realtime.title', 'Real-time Processing'),
      description: t('features.realtime.description', 'Fast generation for immediate results and live applications.'),
      color: 'bg-yellow-500'
    },
    {
      icon: Shield,
      title: t('features.privacy.title', 'Privacy First'),
      description: t('features.privacy.description', 'All processing happens securely with respect for user privacy.'),
      color: 'bg-red-500'
    }
  ];

  return (
    <section id="features" className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            {t('features.title', 'Powerful Features')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('features.description', 'Discover the cutting-edge capabilities that make FreeIndexTTS the most advanced text-to-speech solution.')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6">
              <div className={`w-12 h-12 ${feature.color} rounded-lg flex items-center justify-center mb-4`}>
                <feature.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}