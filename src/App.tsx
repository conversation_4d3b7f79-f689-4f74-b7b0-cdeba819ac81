import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useEffect } from 'react';
import { HomePage } from './pages/HomePage';
import { NotFoundPage } from './pages/NotFoundPage';
import { locales, defaultLocale } from './lib/i18n';

function App() {
  const { i18n } = useTranslation();

  useEffect(() => {
    // 从URL路径中检测语言
    const pathLang = window.location.pathname.split('/')[1];
    if (locales.includes(pathLang as any)) {
      i18n.changeLanguage(pathLang);
    } else {
      // 如果URL中没有语言，重定向到默认语言
      const currentPath = window.location.pathname;
      if (!currentPath.startsWith(`/${defaultLocale}`)) {
        window.history.replaceState(null, '', `/${defaultLocale}${currentPath}`);
        i18n.changeLanguage(defaultLocale);
      }
    }
  }, [i18n]);

  return (
    <Routes>
      <Route path="/" element={<Navigate to={`/${defaultLocale}`} replace />} />
      <Route path="/:locale" element={<HomePage />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
}

export default App; 