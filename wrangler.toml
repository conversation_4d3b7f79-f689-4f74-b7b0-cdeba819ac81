name = "freeindextts"
compatibility_date = "2024-01-01"
pages_build_output_dir = "dist"

# KV namespace for email notifications
[[kv_namespaces]]
binding = "NOTIFICATIONS_KV"
id = "58a911312b8842fca1fb6b8a318bcf0b"
preview_id = "ef5185c2d83c4c7cbd4df8d3ab87e4a1"

# Environment variables
[vars]
FEISHU_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/8b3a45cd-2d0e-4269-8edf-9fb1009ce659"

[env.production]
name = "freeindextts"

# Production KV namespace
[[env.production.kv_namespaces]]
binding = "NOTIFICATIONS_KV"
id = "58a911312b8842fca1fb6b8a318bcf0b"

# Environment variables for production
[env.production.vars]
FEISHU_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/8b3a45cd-2d0e-4269-8edf-9fb1009ce659"

[env.preview]
name = "freeindextts-preview"

# Preview KV namespace
[[env.preview.kv_namespaces]]
binding = "NOTIFICATIONS_KV"
id = "ef5185c2d83c4c7cbd4df8d3ab87e4a1"

# Environment variables for preview
[env.preview.vars]
FEISHU_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/8b3a45cd-2d0e-4269-8edf-9fb1009ce659"