@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --font-inter: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

html {
  font-family: var(--font-inter);
}

body {
  font-family: var(--font-inter);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #ffffff;
  color: #1f2937;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

#root {
  min-height: 100vh;
}

/* Custom utility classes */
.hydration-fix {
  min-height: 100vh;
}

.layout-stable {
  min-height: 100vh;
}

.bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Animation classes */
@keyframes pulse-slow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

/* Loading states */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-primary-600 border-t-transparent;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Focus styles */
.focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Transition classes */
.transition-all {
  transition: all 0.2s ease-in-out;
}

.transition-colors {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
}

.transition-transform {
  transition: transform 0.2s ease-in-out;
}

.transition-shadow {
  transition: box-shadow 0.2s ease-in-out;
}

/* Component specific styles */
.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
  @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
}

.btn-outline {
  @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .text-responsive {
    @apply text-sm;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .text-responsive {
    @apply text-base;
  }
}

@media (min-width: 1025px) {
  .text-responsive {
    @apply text-lg;
  }
} 