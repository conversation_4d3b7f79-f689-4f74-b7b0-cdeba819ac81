import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from './Button';
import { Input } from './Input';
import { Mail, CheckCircle, AlertCircle, Bell, Clock } from 'lucide-react';
import { cn } from '../../lib/utils';
import { validateEmail, ClientRateLimit, formatTimeRemaining } from '../../lib/validation';

interface EmailNotificationProps {
  className?: string;
  variant?: 'inline' | 'modal' | 'banner' | 'compact';
  onSuccess?: (email: string) => void;
  onError?: (error: string) => void;
}

interface SubmissionState {
  status: 'idle' | 'loading' | 'success' | 'error';
  message?: string;
}

export function EmailNotification({
  className,
  variant = 'inline',
  onSuccess,
  onError
}: EmailNotificationProps) {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [submission, setSubmission] = useState<SubmissionState>({ status: 'idle' });
  const [rateLimit] = useState(() => new ClientRateLimit());
  const [rateLimitInfo, setRateLimitInfo] = useState<{ blocked: boolean; resetTime?: number }>({ blocked: false });

  // Check rate limit on component mount and when submission status changes
  useEffect(() => {
    const checkRateLimit = () => {
      const result = rateLimit.checkLimit();
      if (!result.allowed) {
        setRateLimitInfo({ blocked: true, resetTime: result.resetTime });
      } else {
        setRateLimitInfo({ blocked: false });
      }
    };

    checkRateLimit();

    // If rate limited, set up a timer to check when limit resets
    if (rateLimitInfo.blocked && rateLimitInfo.resetTime) {
      const timeUntilReset = rateLimitInfo.resetTime - Date.now();
      if (timeUntilReset > 0) {
        const timer = setTimeout(() => {
          setRateLimitInfo({ blocked: false });
        }, timeUntilReset);

        return () => clearTimeout(timer);
      }
    }
  }, [submission.status, rateLimit, rateLimitInfo.blocked, rateLimitInfo.resetTime]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check client-side rate limiting first
    const rateLimitCheck = rateLimit.checkLimit();
    if (!rateLimitCheck.allowed) {
      const timeRemaining = rateLimitCheck.resetTime ? rateLimitCheck.resetTime - Date.now() : 0;
      setSubmission({
        status: 'error',
        message: t('notification.errors.rateLimited', `Too many requests. Please try again in ${formatTimeRemaining(timeRemaining)}.`)
      });
      setRateLimitInfo({ blocked: true, resetTime: rateLimitCheck.resetTime });
      return;
    }

    // Validate email using the comprehensive validation
    const validation = validateEmail(email.trim(), { checkDisposable: true });
    if (!validation.isValid) {
      setSubmission({
        status: 'error',
        message: validation.error || t('notification.errors.emailInvalid', 'Please enter a valid email address')
      });
      return;
    }

    setSubmission({ status: 'loading' });

    try {
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim() }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to subscribe');
      }

      setSubmission({ 
        status: 'success', 
        message: t('notification.success', 'Thank you! We\'ll notify you when features go live.') 
      });
      setEmail('');
      onSuccess?.(email);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setSubmission({ 
        status: 'error', 
        message: t('notification.errors.submitFailed', 'Failed to subscribe. Please try again.') 
      });
      onError?.(errorMessage);
    }
  };

  const resetForm = () => {
    setSubmission({ status: 'idle' });
    setEmail('');
  };

  const renderContent = () => {
    if (submission.status === 'success') {
      return (
        <div className="text-center">
          <CheckCircle className={cn(
            "text-green-500 mx-auto mb-3",
            variant === 'compact' ? 'w-8 h-8' : 'w-12 h-12 mb-4'
          )} />
          <h3 className={cn(
            "text-gray-900 mb-2",
            variant === 'compact' ? 'text-sm font-medium' : 'text-lg font-semibold'
          )}>
            {t('notification.successTitle', 'You\'re all set!')}
          </h3>
          <p className={cn(
            "text-gray-600 mb-4",
            variant === 'compact' ? 'text-xs mb-3' : ''
          )}>{submission.message}</p>
          <Button
            variant="outline"
            size={variant === 'compact' ? 'sm' : 'sm'}
            onClick={resetForm}
            className={cn("text-sm", variant === 'compact' && 'h-8 px-3 text-xs')}
          >
            {t('notification.subscribeAnother', 'Subscribe another email')}
          </Button>
        </div>
      );
    }

    return (
      <div>
        {variant !== 'compact' ? (
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
              <Bell className="w-5 h-5 text-primary-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {t('notification.title', 'Get notified when we launch')}
              </h3>
              <p className="text-sm text-gray-600">
                {t('notification.description', 'Be the first to know when new features are available')}
              </p>
            </div>
          </div>
        ) : (
          <div className="text-center mb-3">
            <div className="flex items-center justify-center gap-2 mb-1">
              <Bell className="w-4 h-4 text-primary-600" />
              <h4 className="text-sm font-medium text-gray-900">
                {t('notification.title', 'Get notified when we launch')}
              </h4>
            </div>
            <p className="text-xs text-gray-600 leading-relaxed">
              {t('notification.description', 'Be the first to know when new features are available')}
            </p>
          </div>
        )}

        <form onSubmit={handleSubmit} className={variant === 'compact' ? 'space-y-2' : 'space-y-4'}>
          <div className={variant === 'compact' ? 'flex flex-col gap-2 sm:flex-row' : 'flex gap-2'}>
            <div className="flex-1">
              <Input
                type="email"
                placeholder={t('notification.emailPlaceholder', 'Enter your email address')}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={cn(
                  "w-full",
                  variant === 'compact' && "text-sm h-9",
                  submission.status === 'error' && "border-red-500 focus-visible:ring-red-500"
                )}
                disabled={submission.status === 'loading'}
              />
            </div>
            <Button
              type="submit"
              loading={submission.status === 'loading'}
              disabled={!email.trim() || submission.status === 'loading'}
              className={cn("whitespace-nowrap", variant === 'compact' && "text-sm h-9 px-3")}
              size={variant === 'compact' ? 'sm' : 'md'}
            >
              <Mail className="w-4 h-4 mr-1" />
              {t('notification.subscribe', 'Notify Me')}
            </Button>
          </div>

          {submission.status === 'error' && (
            <div className={cn(
              "flex items-center gap-2 text-red-600",
              variant === 'compact' ? 'text-xs justify-center' : 'text-sm'
            )}>
              <AlertCircle className="w-4 h-4 flex-shrink-0" />
              <span className="break-words">{submission.message}</span>
            </div>
          )}
        </form>

        <p className={cn("text-gray-500 mt-3", variant === 'compact' ? 'text-xs text-center' : 'text-xs')}>
          {t('notification.privacy', 'We respect your privacy. Unsubscribe at any time.')}
        </p>
      </div>
    );
  };

  const variantClasses = {
    inline: 'bg-white border border-gray-200 rounded-lg p-6 shadow-sm',
    modal: 'bg-white rounded-lg p-6 max-w-md mx-auto',
    banner: 'bg-gradient-to-r from-primary-50 to-secondary-50 border border-primary-200 rounded-lg p-6',
    compact: 'bg-gray-50/50 border border-dashed border-gray-300 rounded-lg p-4'
  };

  return (
    <div className={cn(variantClasses[variant], className)}>
      {renderContent()}
    </div>
  );
}
