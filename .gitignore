# Dependencies
node_modules/
.npm
.yarn

# Production builds
.next/
out/
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# OpenNext.js build files
.open-next/

# Cloudflare Workers
.wrangler/
worker/

# Analytics and tracking
.plausible/

# Audio files (for development)
*.wav
*.mp3
*.ogg
*.m4a
*.flac

# Large media files
*.mp4
*.avi
*.mov
*.wmv

# Backup files
*.backup
*.bak

# TypeScript
*.tsbuildinfo
next-env.d.ts