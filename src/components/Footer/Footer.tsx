import React from 'react';
import { useTranslation } from 'react-i18next';
import { Github, Twitter, Mail, Heart } from 'lucide-react';

export function Footer() {
  const { t } = useTranslation();

  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">F</span>
              </div>
              <span className="text-xl font-bold">FreeIndexTTS</span>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              {t('footer.description', 'Advanced AI text-to-speech with emotion control and voice cloning using cutting-edge IndexTTS2 technology.')}
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Github className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Mail className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.product', 'Product')}</h3>
            <ul className="space-y-2">
              <li><a href="#features" className="text-gray-300 hover:text-white transition-colors">{t('footer.features', 'Features')}</a></li>
              <li><a href="#demo" className="text-gray-300 hover:text-white transition-colors">{t('footer.demo', 'Demo')}</a></li>
              <li><a href="#faq" className="text-gray-300 hover:text-white transition-colors">{t('footer.faq', 'FAQ')}</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">{t('footer.pricing', 'Pricing')}</a></li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.company', 'Company')}</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">{t('footer.about', 'About')}</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">{t('footer.blog', 'Blog')}</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">{t('footer.careers', 'Careers')}</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">{t('footer.contact', 'Contact')}</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            {t('footer.copyright', '© 2025 FreeIndexTTS. All rights reserved.')}
          </p>
          <div className="flex items-center space-x-1 text-gray-400 text-sm mt-4 md:mt-0">
            <span>{t('footer.madeWith', 'Made with')}</span>
            <Heart className="w-4 h-4 text-red-500" />
            <span>{t('footer.forDevelopers', 'for developers')}</span>
          </div>
        </div>
      </div>
    </footer>
  );
}