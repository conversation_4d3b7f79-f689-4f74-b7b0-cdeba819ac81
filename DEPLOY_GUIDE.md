# FreeIndexTTS 快速部署指南 / Quick Deployment Guide

## ✅ 部署修复完成 / Deployment Fix Complete

所有部署问题已经修复！现在你可以成功部署到 Cloudflare Workers。
All deployment issues have been fixed! You can now successfully deploy to Cloudflare Workers.

## 🚀 部署步骤 / Deployment Steps

### 1. 构建项目 / Build the Project
```bash
npm run deploy:build
```
这将：
- 运行 Next.js 构建
- 使用 OpenNext.js 转换为 Cloudflare Workers 格式
- 生成 `.open-next/` 目录和所需文件

This will:
- Run Next.js build
- Convert using OpenNext.js to Cloudflare Workers format
- Generate `.open-next/` directory with required files

### 2. 安装并登录 Wrangler / Install and Login to Wrangler
```bash
# 安装 Wrangler CLI / Install Wrangler CLI
npm install -g wrangler

# 登录到 Cloudflare / Login to Cloudflare
wrangler login
```

### 3. 部署到 Cloudflare / Deploy to Cloudflare
```bash
npm run deploy:upload
```

### 4. 一键完整部署 / One-Command Full Deployment
```bash
npm run deploy
```
这会依次运行：build → deploy:build → deploy:upload
This runs in sequence: build → deploy:build → deploy:upload

### 5. 访问部署的网站 / Access Deployed Website
部署成功后，你的网站将在以下地址可用：
After successful deployment, your website will be available at:

**Live URL**: `https://freeindextts.macros-hekk.workers.dev`

多语言路由 / Multi-language routes:
- 英文 / English: `https://freeindextts.macros-hekk.workers.dev/en`
- 中文 / Chinese: `https://freeindextts.macros-hekk.workers.dev/zh`
- 日文 / Japanese: `https://freeindextts.macros-hekk.workers.dev/ja`

## 📁 生成的文件 / Generated Files

部署过程中创建的新文件：
New files created during deployment:

- `open-next.config.ts` - OpenNext.js 配置文件
- `wrangler.toml` - Cloudflare Workers 配置文件
- `.open-next/` - 构建输出目录（已在 .gitignore 中）

## ⚙️ 配置说明 / Configuration Details

### open-next.config.ts
```typescript
const config = {
  default: {
    override: {
      wrapper: "cloudflare-node",
      converter: "edge",
      proxyExternalRequest: "fetch",
      incrementalCache: "dummy",
      tagCache: "dummy",
      queue: "dummy"
    }
  },
  // ... middleware 配置
};
```

### wrangler.toml
```toml
name = "freeindextts"
main = ".open-next/worker.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "freeindextts"
```

## 🔧 可选配置 / Optional Configuration

### 环境变量 / Environment Variables
在 `wrangler.toml` 中添加：
Add to `wrangler.toml`:

```toml
[env.production.vars]
NODE_ENV = "production"
PLAUSIBLE_DOMAIN = "freeindextts.com"
```

### KV 存储 / KV Storage
用于缓存的 KV 命名空间：
For caching with KV namespaces:

```toml
[[env.production.kv_namespaces]]
binding = "CACHE"
id = "your-kv-namespace-id"
```

### D1 数据库 / D1 Database
用于分析数据的 D1 数据库：
For analytics data with D1 database:

```toml
[[env.production.d1_databases]]
binding = "DB"
database_name = "freeindextts-db"
database_id = "your-database-id"
```

## 🎯 验证部署 / Verify Deployment

部署成功后，访问你的 Cloudflare Workers 域名：
After successful deployment, visit your Cloudflare Workers domain:

- `https://freeindextts.your-subdomain.workers.dev/en` (英文)
- `https://freeindextts.your-subdomain.workers.dev/zh` (中文)
- `https://freeindextts.your-subdomain.workers.dev/ja` (日文)

## 🛠️ 故障排除 / Troubleshooting

### 常见问题 / Common Issues

1. **构建失败 / Build Failure**
   ```bash
   # 清理并重新构建 / Clean and rebuild
   rm -rf .next .open-next
   npm run build
   npm run deploy:build
   ```

2. **权限错误 / Permission Error**
   ```bash
   # 重新登录 Wrangler / Re-login to Wrangler
   wrangler login
   ```

3. **配置错误 / Configuration Error**
   - 检查 `open-next.config.ts` 格式
   - 检查 `wrangler.toml` 语法
   - Check `open-next.config.ts` format
   - Check `wrangler.toml` syntax

### 日志调试 / Debug Logs
```bash
# 查看详细构建日志 / View detailed build logs
npm run deploy:build -- --verbose

# 查看部署日志 / View deployment logs
wrangler tail freeindextts
```

## 📈 性能优化 / Performance Optimization

### Worker 大小优化 / Worker Size Optimization
- 免费版限制：10MB / Free tier limit: 10MB
- 付费版限制：100MB / Paid tier limit: 100MB
- 当前构建大小约：2-3MB / Current build size: ~2-3MB

### 缓存策略 / Caching Strategy
- 静态资源：1年缓存 / Static assets: 1 year cache
- HTML 页面：1小时缓存 / HTML pages: 1 hour cache
- API 响应：5分钟缓存 / API responses: 5 minutes cache

---

🎉 **恭喜！现在你可以成功部署 FreeIndexTTS 到 Cloudflare Workers 了！**

🎉 **Congratulations! You can now successfully deploy FreeIndexTTS to Cloudflare Workers!**