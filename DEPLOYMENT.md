# FreeIndexTTS 部署教程 / Deployment Guide

这个文档提供了 FreeIndexTTS 的完整部署指南，包括本地开发、Cloudflare Workers 部署和其他平台部署方案。

This document provides a comprehensive deployment guide for FreeIndexTTS, including local development, Cloudflare Workers deployment, and other platform deployment options.

## 🚀 快速开始 / Quick Start

### 环境要求 / Prerequisites

- Node.js 18+ 
- npm 或 yarn / npm or yarn
- Git

### 本地开发 / Local Development

1. **克隆仓库 / Clone Repository**
```bash
git clone https://github.com/your-username/freeindextts.git
cd freeindextts
```

2. **安装依赖 / Install Dependencies**
```bash
npm install
# 或者 / or
yarn install
```

3. **启动开发服务器 / Start Development Server**
```bash
npm run dev
# 或者 / or  
yarn dev
```

4. **访问网站 / Access Website**
   - 英文版 / English: http://localhost:3000/en
   - 中文版 / Chinese: http://localhost:3000/zh
   - 日文版 / Japanese: http://localhost:3000/ja

## ☁️ Cloudflare Workers 部署 / Cloudflare Workers Deployment

### 为什么选择 Cloudflare Workers? / Why Cloudflare Workers?

- **全球CDN**: 超过270个数据中心
- **零冷启动**: 边缘计算无延迟
- **成本效益**: 每月10万请求免费
- **自动扩展**: 无需配置服务器
- **高性能**: 毫秒级响应时间

- **Global CDN**: 270+ data centers worldwide
- **Zero Cold Start**: Edge computing with no latency
- **Cost Effective**: 100k requests/month free
- **Auto Scaling**: No server configuration needed
- **High Performance**: Millisecond response times

### 步骤 / Steps

1. **构建项目 / Build Project**
```bash
npm run build
```

2. **使用 OpenNext.js 转换 / Convert with OpenNext.js**
```bash
npm run deploy:build
# 或者直接运行 / or run directly
npx opennextjs-cloudflare build
```

3. **安装 Wrangler CLI / Install Wrangler CLI**
```bash
npm install -g wrangler
```

4. **登录 Cloudflare / Login to Cloudflare**
```bash
wrangler login
```

5. **部署到 Cloudflare / Deploy to Cloudflare**
```bash
npm run deploy:upload
# 或者直接运行 / or run directly
npx opennextjs-cloudflare upload
```

6. **一键部署 / One-command Deployment**
```bash
npm run deploy
# 这将依次运行: build -> deploy:build -> deploy:upload
# This will run in sequence: build -> deploy:build -> deploy:upload
```

### 环境变量配置 / Environment Variables

在 Cloudflare Workers 中配置以下环境变量：
Configure the following environment variables in Cloudflare Workers:

```bash
# Plausible Analytics
PLAUSIBLE_DOMAIN=freeindextts.com

# IndexTTS2 API (将来使用 / Future use)
INDEXTTS2_API_URL=https://api.indextts2.com
INDEXTTS2_API_KEY=your_api_key_here

# 数据库配置 / Database Configuration  
DATABASE_URL=your_d1_database_url
```

### 自定义域名 / Custom Domain

1. **在 Cloudflare 控制台中添加域名**
   Add domain in Cloudflare dashboard

2. **配置 DNS 记录**
   Configure DNS records:
```
Type: CNAME
Name: @
Target: your-worker-name.your-subdomain.workers.dev
```

3. **启用 SSL/TLS**
   Enable SSL/TLS with "Full (strict)" mode

## 🌍 其他部署平台 / Other Deployment Platforms

### Vercel 部署 / Vercel Deployment

1. **连接 GitHub 仓库**
   Connect GitHub repository

2. **配置构建设置**
   Configure build settings:
```bash
Build Command: npm run build
Output Directory: .next
Install Command: npm install
```

3. **环境变量设置**
   Environment variables setup:
```bash
NEXT_PUBLIC_PLAUSIBLE_DOMAIN=freeindextts.com
```

4. **部署命令**
   Deploy command:
```bash
vercel --prod
```

### Netlify 部署 / Netlify Deployment

1. **创建 netlify.toml**
   Create netlify.toml:
```toml
[build]
  command = "npm run build"
  publish = ".next"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NEXT_PUBLIC_PLAUSIBLE_DOMAIN = "freeindextts.com"
```

2. **部署到 Netlify**
   Deploy to Netlify:
```bash
netlify deploy --prod --dir=.next
```

### AWS 部署 / AWS Deployment

使用 AWS CDK 或 Serverless Framework 部署到 AWS Lambda。
Deploy to AWS Lambda using AWS CDK or Serverless Framework.

#### Serverless Framework 示例 / Example

1. **安装 Serverless**
   Install Serverless:
```bash
npm install -g serverless
```

2. **创建 serverless.yml**
   Create serverless.yml:
```yaml
service: freeindextts

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1

functions:
  app:
    handler: handler.app
    events:
      - http:
          path: /{proxy+}
          method: ANY
      - http:
          path: /
          method: ANY

plugins:
  - serverless-next.js
```

3. **部署**
   Deploy:
```bash
serverless deploy
```

## 📊 性能优化 / Performance Optimization

### Core Web Vitals 优化 / Core Web Vitals Optimization

1. **图片优化 / Image Optimization**
```typescript
// 使用 Next.js Image 组件
import Image from 'next/image'

<Image
  src="/demo-thumbnail.jpg"
  alt="IndexTTS2 Demo"
  width={600}
  height={400}
  loading="lazy"
  quality={85}
/>
```

2. **字体优化 / Font Optimization**
```typescript
// 在 layout.tsx 中预加载字体
import { Inter } from 'next/font/google'

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  preload: true
})
```

3. **代码分割 / Code Splitting**
```typescript
// 动态导入组件
const VideoDemo = dynamic(() => import('@/components/Demo/VideoDemo'), {
  loading: () => <p>Loading...</p>,
  ssr: false
})
```

### 缓存策略 / Caching Strategy

1. **Cloudflare 缓存规则**
   Cloudflare Cache Rules:
```bash
# 静态资源缓存1年
Cache-Control: public, max-age=31536000, immutable

# HTML 缓存1小时
Cache-Control: public, max-age=3600, stale-while-revalidate=86400
```

2. **Browser 缓存**
   Browser Caching:
```typescript
// next.config.js
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)\\.(png|jpg|jpeg|gif|svg|ico|webp)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      }
    ]
  }
}
```

## 🔧 监控和维护 / Monitoring and Maintenance

### Plausible Analytics 设置 / Plausible Analytics Setup

1. **在 Plausible 中添加网站**
   Add website in Plausible

2. **配置自定义事件**
   Configure custom events:
```typescript
// 跟踪 TTS 生成
plausible('TTS Generation', {
  props: {
    language: 'en',
    textLength: 150,
    hasEmotion: true
  }
})
```

### 错误监控 / Error Monitoring

推荐使用 Sentry 进行错误监控：
Recommended using Sentry for error monitoring:

```bash
npm install @sentry/nextjs
```

```typescript
// sentry.client.config.ts
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0
})
```

### 健康检查 / Health Checks

创建健康检查端点：
Create health check endpoint:

```typescript
// app/api/health/route.ts
export async function GET() {
  return Response.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  })
}
```

## 🔐 安全配置 / Security Configuration

### HTTPS 强制 / HTTPS Enforcement

```typescript
// next.config.js
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains'
          }
        ]
      }
    ]
  }
}
```

### CSP 内容安全策略 / CSP Content Security Policy

```typescript
const cspHeader = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline' plausible.io;
  style-src 'self' 'unsafe-inline';
  img-src 'self' blob: data:;
  font-src 'self';
  connect-src 'self' plausible.io;
  media-src 'self' mpvideo.qpic.cn;
`
```

## 📱 移动端优化 / Mobile Optimization

### PWA 配置 / PWA Configuration

1. **安装 next-pwa**
   Install next-pwa:
```bash
npm install next-pwa
```

2. **配置 manifest.json**
   Configure manifest.json:
```json
{
  "name": "FreeIndexTTS",
  "short_name": "IndexTTS",
  "description": "Advanced AI Text-to-Speech",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#0ea5e9",
  "icons": [
    {
      "src": "/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    }
  ]
}
```

## 📈 SEO 优化 / SEO Optimization

### 元数据优化 / Metadata Optimization

确保每个页面都有适当的元数据：
Ensure each page has proper metadata:

```typescript
export const metadata: Metadata = {
  title: 'FreeIndexTTS - IndexTTS2 AI Voice Synthesis',
  description: 'Revolutionary IndexTTS2 text-to-speech with emotion control and zero-shot voice cloning',
  keywords: 'IndexTTS2, text-to-speech, TTS, voice cloning, AI voice synthesis',
  openGraph: {
    title: 'FreeIndexTTS - Advanced AI Text-to-Speech',
    description: 'Experience IndexTTS2 breakthrough voice synthesis technology',
    images: ['/og-image.jpg']
  }
}
```

### 结构化数据 / Structured Data

```json
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "FreeIndexTTS",
  "applicationCategory": "MultimediaApplication",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD"
  }
}
```

## 🚨 故障排除 / Troubleshooting

### 常见问题 / Common Issues

1. **构建失败 / Build Failures**
```bash
# 清除缓存
npm run build --clean
# 或者
rm -rf .next node_modules
npm install
npm run build
```

2. **多语言路由问题 / i18n Routing Issues**
```typescript
// 检查 middleware.ts 配置
export const config = {
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)']
}
```

3. **Cloudflare Workers 限制 / Cloudflare Workers Limits**
   - 免费版 CPU 时间限制：10ms
   - 内存限制：128MB
   - 脚本大小限制：10MB (压缩后)

### 日志调试 / Logging and Debugging

```typescript
// 开发环境日志
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', { locale, params })
}

// 生产环境使用结构化日志
import { logger } from '@/lib/logger'
logger.info('TTS generation completed', { 
  userId, 
  textLength, 
  processingTime 
})
```

## 📞 支持和社区 / Support and Community

### 获取帮助 / Getting Help

- **GitHub Issues**: https://github.com/freeindextts/freeindextts/issues
- **文档**: https://freeindextts.com/docs
- **社区论坛**: https://community.freeindextts.com
- **邮件支持**: <EMAIL>

### 贡献指南 / Contributing

1. Fork 项目 / Fork the project
2. 创建功能分支 / Create feature branch
3. 提交更改 / Commit changes
4. 推送到分支 / Push to branch
5. 创建 Pull Request / Create Pull Request

---

**祝您部署顺利！如有问题，请随时联系我们的社区支持。**

**Happy deploying! If you have any issues, feel free to reach out to our community support.**