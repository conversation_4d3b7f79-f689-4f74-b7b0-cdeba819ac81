# Email Notification System Deployment Guide

## Overview

This guide covers the deployment of the new email notification system for FreeIndexTTS, which includes:

- **Email Collection UI**: React component with validation and user feedback
- **Cloudflare Workers API**: Backend for handling email submissions
- **KV Storage**: Persistent storage for email addresses
- **Feishu Integration**: Real-time notifications to Feishu webhook
- **Rate Limiting**: Client and server-side spam prevention
- **Internationalization**: Support for English, Chinese, and Japanese

## Prerequisites

1. **Cloudflare Account**: With Pages and Workers access
2. **Wrangler CLI**: Installed and authenticated
3. **Node.js**: Version 18+ for local development

## Deployment Steps

### 1. Set Up KV Namespaces

First, create the required KV namespaces for storing email data:

```bash
# Run the automated setup script
./scripts/setup-kv.sh
```

Or manually create namespaces:

```bash
# Create production namespace
wrangler kv:namespace create "NOTIFICATIONS_KV" --preview false

# Create preview namespace  
wrangler kv:namespace create "NOTIFICATIONS_KV" --preview true
```

Update `wrangler.toml` with the returned namespace IDs.

### 2. Configure Environment Variables

The system uses the following environment variables:

- `FEISHU_WEBHOOK_URL`: Feishu webhook URL (default provided)
- `NOTIFICATIONS_KV`: KV namespace binding (configured in wrangler.toml)

### 3. Deploy to Cloudflare Pages

```bash
# Build the project
npm run build

# Deploy to Cloudflare Pages
npm run deploy
```

### 4. Verify Deployment

After deployment, test the notification system:

1. **Visit your site**: Navigate to any page with the notification section
2. **Test email submission**: Try subscribing with a valid email
3. **Check Feishu**: Verify notification appears in Feishu channel
4. **Test validation**: Try invalid emails, rate limiting, etc.

## Features Implemented

### Email Collection Component

- **Location**: Integrated into TTSWorkspace OutputPanel "Coming Soon" section
- **Variants**: Uses inline style with dashed border design
- **Validation**: Client-side email format validation
- **Rate Limiting**: 3 requests per minute per client
- **Internationalization**: Full i18n support
- **Context**: Perfectly positioned where users see "Coming Soon" status

### Backend API

- **Endpoint**: `/api/notifications/subscribe`
- **Method**: POST
- **Rate Limiting**: Server-side protection
- **Validation**: Email format and disposable email detection
- **Storage**: KV namespace with timestamps and metadata
- **Notifications**: Automatic Feishu webhook integration

### Data Storage Structure

```typescript
// Email storage key: `email:${email.toLowerCase()}`
interface EmailSubscription {
  email: string;
  timestamp: string;
  ip?: string;
  userAgent?: string;
}

// List storage key: `email_list`
// Contains array of all subscriptions

// Rate limiting key: `rate_limit:${ip}`
// Contains request count with TTL
```

## Testing

### Local Testing

```bash
# Start development server
npm run dev

# Test the notification component
# Navigate to http://localhost:3000/en
```

### Production Testing

1. **Email Validation**:
   - Valid email: `<EMAIL>`
   - Invalid email: `invalid-email`
   - Disposable email: `<EMAIL>`

2. **Rate Limiting**:
   - Submit 4+ requests quickly
   - Should show rate limit message

3. **Feishu Integration**:
   - Submit valid email
   - Check Feishu channel for notification

### Monitoring

Check KV storage contents:

```bash
# List all stored emails
wrangler kv:key list --namespace-id YOUR_KV_NAMESPACE_ID

# Get email list
wrangler kv:key get "email_list" --namespace-id YOUR_KV_NAMESPACE_ID

# Get specific email
wrangler kv:key get "email:<EMAIL>" --namespace-id YOUR_KV_NAMESPACE_ID
```

## Troubleshooting

### Common Issues

1. **KV Namespace Not Found**
   - Ensure namespaces are created and IDs are correct in wrangler.toml
   - Check binding names match between code and configuration

2. **Feishu Notifications Not Working**
   - Verify webhook URL is correct
   - Check Feishu bot permissions
   - Review function logs in Cloudflare dashboard

3. **Rate Limiting Too Aggressive**
   - Adjust limits in `functions/api/notifications/subscribe.ts`
   - Clear localStorage for client-side testing

4. **Email Validation Issues**
   - Check validation logic in `src/lib/validation.ts`
   - Test with various email formats

### Debugging

Enable debug logging:

```typescript
// In functions/api/notifications/subscribe.ts
console.log('Debug info:', { email, ip, userAgent });
```

View logs in Cloudflare dashboard under Functions > Logs.

## Security Considerations

- **Rate Limiting**: Prevents spam and abuse
- **Email Validation**: Blocks invalid and disposable emails
- **IP Tracking**: For rate limiting and analytics
- **CORS**: Properly configured for cross-origin requests
- **Data Privacy**: Minimal data collection, clear privacy notice

## Future Enhancements

1. **Email Verification**: Send confirmation emails
2. **Unsubscribe System**: Allow users to opt out
3. **Analytics Dashboard**: View subscription metrics
4. **A/B Testing**: Test different notification designs
5. **Webhook Security**: Add signature verification for Feishu

## Support

For issues or questions:
1. Check Cloudflare dashboard logs
2. Review KV namespace contents
3. Test locally with `npm run dev`
4. Verify wrangler.toml configuration
