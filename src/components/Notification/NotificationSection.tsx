import React from 'react';
import { useTranslation } from 'react-i18next';
import { EmailNotification } from '../ui/EmailNotification';
import { Bell, Sparkles, Zap } from 'lucide-react';

export function NotificationSection() {
  const { t } = useTranslation();

  const handleSuccess = (email: string) => {
    console.log('Email subscription successful:', email);
    // You could add analytics tracking here
  };

  const handleError = (error: string) => {
    console.error('Email subscription error:', error);
    // You could add error tracking here
  };

  return (
    <section className="py-16 bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-full flex items-center justify-center">
                <Bell className="w-8 h-8 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-accent-500 rounded-full flex items-center justify-center">
                <Sparkles className="w-3 h-3 text-white" />
              </div>
            </div>
          </div>
          
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-secondary-600">
              {t('notification.sectionTitle', 'Stay Updated')}
            </span>
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            {t('notification.sectionDescription', 'IndexTTS2 is coming soon with revolutionary voice cloning and emotion control. Be among the first to experience the future of AI text-to-speech.')}
          </p>

          {/* Feature highlights */}
          <div className="flex flex-wrap justify-center gap-6 mb-12">
            <div className="flex items-center gap-2 bg-white rounded-full px-4 py-2 shadow-sm">
              <Zap className="w-4 h-4 text-primary-600" />
              <span className="text-sm font-medium text-gray-700">
                {t('notification.features.instant', 'Instant notifications')}
              </span>
            </div>
            <div className="flex items-center gap-2 bg-white rounded-full px-4 py-2 shadow-sm">
              <Bell className="w-4 h-4 text-secondary-600" />
              <span className="text-sm font-medium text-gray-700">
                {t('notification.features.exclusive', 'Exclusive early access')}
              </span>
            </div>
            <div className="flex items-center gap-2 bg-white rounded-full px-4 py-2 shadow-sm">
              <Sparkles className="w-4 h-4 text-accent-600" />
              <span className="text-sm font-medium text-gray-700">
                {t('notification.features.updates', 'Feature updates')}
              </span>
            </div>
          </div>
        </div>

        {/* Email notification component */}
        <div className="max-w-2xl mx-auto">
          <EmailNotification
            variant="banner"
            onSuccess={handleSuccess}
            onError={handleError}
            className="shadow-lg"
          />
        </div>

        {/* Additional info */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            {t('notification.additionalInfo', 'Join thousands of developers and content creators waiting for IndexTTS2')}
          </p>
        </div>
      </div>
    </section>
  );
}
