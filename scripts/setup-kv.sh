#!/bin/bash

# Setup script for Cloudflare KV namespaces for email notifications
# This script creates the necessary KV namespaces for the notification system

echo "🚀 Setting up Cloudflare KV namespaces for email notifications..."

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI is not installed. Please install it first:"
    echo "   npm install -g wrangler"
    exit 1
fi

# Check if user is logged in
if ! wrangler whoami &> /dev/null; then
    echo "❌ You are not logged in to Cloudflare. Please login first:"
    echo "   wrangler login"
    exit 1
fi

echo "✅ Wrangler CLI is installed and you are logged in."

# Create production KV namespace
echo "📦 Creating production KV namespace..."
PROD_KV_ID=$(wrangler kv namespace create "NOTIFICATIONS_KV" --preview false | grep -o 'id = "[^"]*"' | cut -d'"' -f2)

if [ -z "$PROD_KV_ID" ]; then
    echo "❌ Failed to create production KV namespace"
    exit 1
fi

echo "✅ Production KV namespace created with ID: $PROD_KV_ID"

# Create preview KV namespace
echo "📦 Creating preview KV namespace..."
PREVIEW_KV_ID=$(wrangler kv namespace create "NOTIFICATIONS_KV" --preview true | grep -o 'id = "[^"]*"' | cut -d'"' -f2)

if [ -z "$PREVIEW_KV_ID" ]; then
    echo "❌ Failed to create preview KV namespace"
    exit 1
fi

echo "✅ Preview KV namespace created with ID: $PREVIEW_KV_ID"

# Update wrangler.toml with the actual KV namespace IDs
echo "📝 Updating wrangler.toml with KV namespace IDs..."

# Create a backup of the original file
cp wrangler.toml wrangler.toml.backup

# Update the production KV namespace ID
sed -i.tmp "s/id = \"notifications_kv_namespace\"/id = \"$PROD_KV_ID\"/g" wrangler.toml

# Update the preview KV namespace ID
sed -i.tmp "s/id = \"notifications_kv_namespace_preview\"/id = \"$PREVIEW_KV_ID\"/g" wrangler.toml

# Clean up temporary files
rm -f wrangler.toml.tmp

echo "✅ wrangler.toml updated with actual KV namespace IDs"

echo ""
echo "🎉 KV namespaces setup complete!"
echo ""
echo "📋 Summary:"
echo "   Production KV ID: $PROD_KV_ID"
echo "   Preview KV ID: $PREVIEW_KV_ID"
echo ""
echo "🔧 Next steps:"
echo "   1. Deploy your Pages project: npm run deploy"
echo "   2. Test the notification system"
echo ""
echo "💡 To view stored emails later, use:"
echo "   wrangler kv:key list --namespace-id $PROD_KV_ID"
echo "   wrangler kv:key get \"email_list\" --namespace-id $PROD_KV_ID"
