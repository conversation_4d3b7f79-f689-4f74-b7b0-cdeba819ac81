'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

export function FAQ() {
  const { t } = useTranslation();
  const [openItems, setOpenItems] = useState<string[]>([]);

  const faqs = [
    {
      id: 'what-is-indextts',
      question: t('faq.whatIsIndexTTS.question', 'What is IndexTTS2?'),
      answer: t('faq.whatIsIndexTTS.answer', 'IndexTTS2 is a breakthrough text-to-speech model that enables zero-shot voice cloning, emotion control, and natural speech generation from any text input.')
    },
    {
      id: 'how-voice-cloning-works',
      question: t('faq.howVoiceCloningWorks.question', 'How does voice cloning work?'),
      answer: t('faq.howVoiceCloningWorks.answer', 'Our AI analyzes a short audio sample of the target voice and learns to replicate its characteristics, tone, and speaking style without requiring extensive training data.')
    },
    {
      id: 'supported-languages',
      question: t('faq.supportedLanguages.question', 'Which languages are supported?'),
      answer: t('faq.supportedLanguages.answer', 'FreeIndexTTS supports multiple languages including English, Chinese, Japanese, and more. The model can generate natural-sounding speech in each supported language.')
    },
    {
      id: 'commercial-use',
      question: t('faq.commercialUse.question', 'Can I use this for commercial purposes?'),
      answer: t('faq.commercialUse.answer', 'Please review our terms of service and licensing agreements for commercial use guidelines. We offer different licensing options for various use cases.')
    },
    {
      id: 'audio-quality',
      question: t('faq.audioQuality.question', 'What audio quality can I expect?'),
      answer: t('faq.audioQuality.answer', 'Our model generates high-quality audio with natural intonation, proper pronunciation, and emotional expressiveness that closely matches human speech.')
    },
    {
      id: 'privacy-security',
      question: t('faq.privacySecurity.question', 'How is my data protected?'),
      answer: t('faq.privacySecurity.answer', 'We prioritize user privacy and data security. All processing is done securely, and we do not store your input text or generated audio files.')
    }
  ];

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <section id="faq" className="py-16 bg-white">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            {t('faq.title', 'Frequently Asked Questions')}
          </h2>
          <p className="text-lg text-gray-600">
            {t('faq.description', 'Find answers to common questions about FreeIndexTTS')}
          </p>
        </div>

        <div className="space-y-4">
          {faqs.map((item) => (
            <div key={item.id} className="bg-gray-50 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleItem(item.id)}
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-100 transition-colors"
              >
                <span className="font-medium text-gray-900">{item.question}</span>
                <ChevronDown 
                  className={`w-5 h-5 text-gray-500 transition-transform ${
                    openItems.includes(item.id) && 'rotate-180'
                  }`}
                />
              </button>
              
              {openItems.includes(item.id) && (
                <div className="px-6 pb-4">
                  <p className="text-gray-600 leading-relaxed">{item.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}