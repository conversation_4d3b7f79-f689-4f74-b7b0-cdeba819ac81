{"name": "freeindextts", "version": "1.0.0", "description": "Advanced AI text-to-speech with IndexTTS2 - emotion control and voice cloning", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite", "deploy": "npm run build && npm run deploy:pages", "deploy:pages": "wrangler pages deploy dist --project-name freeindextts"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "react-i18next": "^13.5.0", "i18next": "^23.7.0", "i18next-browser-languagedetector": "^7.2.0", "clsx": "^2.0.0", "lucide-react": "^0.300.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0", "wrangler": "^3.20.0"}, "keywords": ["text-to-speech", "tts", "voice-cloning", "ai", "indextts2", "emotion-control", "multilingual", "cloudflare-pages", "vite", "react"], "author": "FreeIndexTTS", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}