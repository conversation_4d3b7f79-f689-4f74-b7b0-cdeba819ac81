'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import { Mic, Wand2, Clock, Globe } from 'lucide-react';
import { useAnalytics } from '../../lib/analytics';

export function Hero() {
  const { t } = useTranslation();
  const { trackFeatureUsage } = useAnalytics();

  const features = [
    {
      icon: Mic,
      title: t('hero.features.voiceCloning', 'Voice Cloning'),
      color: 'text-blue-600'
    },
    {
      icon: Wand2,
      title: t('hero.features.emotionControl', 'Emotion Control'),
      color: 'text-purple-600'
    },
    {
      icon: Clock,
      title: t('hero.features.durationControl', 'Duration Control'),
      color: 'text-green-600'
    },
    {
      icon: Globe,
      title: t('hero.features.multilingual', 'Multilingual'),
      color: 'text-orange-600'
    }
  ];

  const handleGetStarted = () => {
    trackFeatureUsage('hero_get_started');
    document.getElementById('workspace')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          {/* Main Heading */}
          <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-secondary-600">
              {t('hero.title', 'Advanced AI')}
            </span>
            <br />
            {/* {t('hero.subtitle', 'Text-to-Speech')} */}
          </h1>
          
          {/* Subtitle */}
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            {t('hero.description', 'Transform your text into lifelike speech with emotion control, voice cloning, and duration adjustments using cutting-edge IndexTTS2 technology.')}
          </p>
          
          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Button 
              onClick={handleGetStarted}
              className="bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white px-8 py-3 text-lg font-semibold rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105"
            >
              {t('hero.cta.getStarted', 'Get Started Free')}
            </Button>
            <Button 
              variant="outline"
              onClick={() => document.getElementById('demo')?.scrollIntoView({ behavior: 'smooth' })}
              className="border-primary-600 text-primary-600 hover:bg-primary-50 px-8 py-3 text-lg font-semibold rounded-lg transition-all duration-200"
            >
              {t('hero.cta.watchDemo', 'Watch Demo')}
            </Button>
          </div>
          
          {/* Features */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {features.map((feature, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center mx-auto mb-4 group-hover:shadow-xl transition-shadow duration-200">
                  <feature.icon className={`w-8 h-8 ${feature.color}`} />
                </div>
                <h3 className="text-sm font-semibold text-gray-900">
                  {feature.title}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}